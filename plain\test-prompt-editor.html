<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词编辑器功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
    </style>
</head>
<body>
    <h1>🧪 提示词编辑器功能测试</h1>
    
    <div class="test-section">
        <h2>📋 测试清单</h2>
        <div id="test-results">
            <div class="info">准备运行测试...</div>
        </div>
        <button onclick="runAllTests()">🚀 运行所有测试</button>
        <button onclick="clearResults()">🧹 清理结果</button>
    </div>

    <div class="test-section">
        <h2>🎯 手动功能测试</h2>
        <p>点击下面的按钮测试各项功能：</p>
        
        <button onclick="testOpenEditor()">📝 测试打开编辑器</button>
        <button onclick="testDataAdaptation()">🔄 测试数据适配</button>
        <button onclick="testSearchFunction()">🔍 测试搜索功能</button>
        <button onclick="testExportFunction()">📤 测试导出功能</button>
        <button onclick="testMainIntegration()">🔗 测试主界面集成</button>
        
        <div id="manual-test-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 系统状态</h2>
        <div id="system-status">
            <div class="info">加载系统状态中...</div>
        </div>
        <button onclick="checkSystemStatus()">🔍 检查系统状态</button>
    </div>

    <!-- 引入必要的脚本文件 -->
    <script src="channel-detection-editor/dependency-injection.js"></script>
    <script src="channel-detection-editor/local-storage-manager.js"></script>
    <script src="channel-detection-editor/field-mapper.js"></script>
    <script src="channel-detection-editor/prompt-composer.js"></script>
    <script src="channel-detection-editor/gemini-service.js"></script>
    <script src="channel-detection-editor/prompt-editor.js"></script>
    <script src="channel-detection-editor/main.js"></script>

    <script>
        let testResults = [];
        let promptEditor = null;

        // 初始化系统
        async function initializeSystem() {
            try {
                await window.initializeSystem();
                promptEditor = window.container.get('promptEditor');
                addResult('✅ 系统初始化成功', 'success');
                return true;
            } catch (error) {
                addResult('❌ 系统初始化失败: ' + error.message, 'error');
                return false;
            }
        }

        // 添加测试结果
        function addResult(message, type = 'info') {
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('test-results').appendChild(result);
            testResults.push({ message, type, timestamp: new Date() });
        }

        // 清理结果
        function clearResults() {
            document.getElementById('test-results').innerHTML = '<div class="info">测试结果已清理</div>';
            document.getElementById('manual-test-results').innerHTML = '';
            testResults = [];
        }

        // 运行所有自动化测试
        async function runAllTests() {
            clearResults();
            addResult('🚀 开始运行自动化测试套件', 'info');

            // 测试1: 系统初始化
            const systemReady = await initializeSystem();
            if (!systemReady) {
                addResult('❌ 系统初始化失败，停止测试', 'error');
                return;
            }

            // 测试2: PromptEditor实例化
            try {
                if (promptEditor && typeof promptEditor.openEditor === 'function') {
                    addResult('✅ PromptEditor实例化成功', 'success');
                } else {
                    addResult('❌ PromptEditor实例化失败或缺少openEditor方法', 'error');
                }
            } catch (error) {
                addResult('❌ PromptEditor测试失败: ' + error.message, 'error');
            }

            // 测试3: 数据适配层
            try {
                const testData = { klook: { customer: { content: '测试提示词' } } };
                const adapted = promptEditor.mapPromptsObjectToSnippetsArray(testData);
                if (Array.isArray(adapted) && adapted.length > 0) {
                    addResult('✅ 数据适配层工作正常', 'success');
                } else {
                    addResult('❌ 数据适配层返回格式错误', 'error');
                }
            } catch (error) {
                addResult('❌ 数据适配层测试失败: ' + error.message, 'error');
            }

            // 测试4: 缓存系统
            try {
                promptEditor.searchCache.set('test', ['test']);
                if (promptEditor.searchCache.get('test')) {
                    addResult('✅ 缓存系统工作正常', 'success');
                } else {
                    addResult('❌ 缓存系统异常', 'error');
                }
            } catch (error) {
                addResult('❌ 缓存系统测试失败: ' + error.message, 'error');
            }

            // 测试5: 统计功能
            try {
                const stats = promptEditor.getSnippetsStats();
                if (stats && typeof stats.total === 'number') {
                    addResult(`✅ 统计功能正常 - 总片段数: ${stats.total}`, 'success');
                } else {
                    addResult('❌ 统计功能异常', 'error');
                }
            } catch (error) {
                addResult('❌ 统计功能测试失败: ' + error.message, 'error');
            }

            addResult('🎉 自动化测试完成', 'info');
        }

        // 手动测试函数
        function testOpenEditor() {
            try {
                promptEditor.openEditor();
                addManualResult('✅ 编辑器打开成功 - 请检查是否显示模态框', 'success');
            } catch (error) {
                addManualResult('❌ 编辑器打开失败: ' + error.message, 'error');
            }
        }

        function testDataAdaptation() {
            try {
                const testData = { 
                    klook: { customer: { content: '提取客户姓名', name: '客户提取' } },
                    kkday: { phone: { content: '提取电话号码', name: '电话提取' } }
                };
                const result = promptEditor.mapPromptsObjectToSnippetsArray(testData);
                addManualResult(`✅ 数据适配成功 - 转换了 ${result.length} 个片段`, 'success');
                console.log('适配结果:', result);
            } catch (error) {
                addManualResult('❌ 数据适配失败: ' + error.message, 'error');
            }
        }

        function testSearchFunction() {
            try {
                promptEditor.filterSnippets('customer');
                addManualResult('✅ 搜索功能调用成功 - 请检查控制台日志', 'success');
            } catch (error) {
                addManualResult('❌ 搜索功能失败: ' + error.message, 'error');
            }
        }

        function testExportFunction() {
            try {
                promptEditor.exportAllData();
                addManualResult('✅ 导出功能调用成功 - 应该开始下载文件', 'success');
            } catch (error) {
                addManualResult('❌ 导出功能失败: ' + error.message, 'error');
            }
        }

        function testMainIntegration() {
            try {
                promptEditor.initializeMainInterfaceIntegration();
                addManualResult('✅ 主界面集成初始化成功', 'success');
            } catch (error) {
                addManualResult('❌ 主界面集成失败: ' + error.message, 'error');
            }
        }

        function addManualResult(message, type) {
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('manual-test-results').appendChild(result);
        }

        function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            try {
                const status = {
                    container: !!window.container,
                    promptEditor: !!promptEditor,
                    openEditor: !!(promptEditor && promptEditor.openEditor),
                    snippetsCount: promptEditor ? promptEditor.promptSnippets.length : 0,
                    cacheStats: promptEditor ? promptEditor.getCacheStats() : null
                };

                statusDiv.innerHTML = `
                    <div class="test-result success">
                        <strong>系统状态检查:</strong><br>
                        • 依赖容器: ${status.container ? '✅' : '❌'}<br>
                        • PromptEditor: ${status.promptEditor ? '✅' : '❌'}<br>
                        • openEditor方法: ${status.openEditor ? '✅' : '❌'}<br>
                        • 片段数量: ${status.snippetsCount}<br>
                        • 搜索缓存: ${status.cacheStats?.searchCache.size || 0} 项<br>
                        • 渲染缓存: ${status.cacheStats?.renderCache.size || 0} 项
                    </div>
                `;
            } catch (error) {
                statusDiv.innerHTML = `<div class="test-result error">状态检查失败: ${error.message}</div>`;
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', async () => {
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待脚本加载
            checkSystemStatus();
        });
    </script>
</body>
</html>
