/**
 * 简单测试：验证基本功能
 */
console.log('🧪 开始简单测试...');

// 模拟localStorageManager
const mockLocalStorage = {
    loadData: async (key) => {
        console.log(`📖 模拟加载: ${key}`);
        if (key === 'channelDetectionRules') {
            return {
                klook: { name: 'Klook', patterns: ['klook'], confidence: 0.85 },
                ctrip: { name: 'Ctrip', patterns: ['ctrip'], confidence: 0.8 }
            };
        }
        return null;
    },
    saveData: async (key, data) => {
        console.log(`💾 模拟保存: ${key}`, Object.keys(data || {}));
    }
};

// 简单的同步机制测试
class SimpleChannelManager {
    constructor(storage) {
        this.storage = storage;
        this.channels = new Set();
        this.listeners = [];
    }

    async initialize() {
        const rules = await this.storage.loadData('channelDetectionRules');
        if (rules) {
            Object.keys(rules).forEach(channel => this.channels.add(channel));
        }
        console.log('📋 初始化渠道:', Array.from(this.channels));
    }

    addListener(callback) {
        this.listeners.push(callback);
    }

    publishUpdate(channels, source) {
        console.log(`📡 ${source} 发布更新:`, channels);
        this.listeners.forEach(listener => {
            try {
                listener('channelsUpdated', { channels, source });
            } catch (e) {
                console.error('监听器错误:', e);
            }
        });
    }

    getAllChannels() {
        return Array.from(this.channels);
    }
}

// 测试
async function runTest() {
    const manager = new SimpleChannelManager(mockLocalStorage);

    // 添加监听器
    manager.addListener((eventType, data) => {
        console.log(`� 收到事件: ${eventType}`, data);
    });

    // 初始化
    await manager.initialize();

    // 模拟更新
    manager.publishUpdate(['klook', 'ctrip', 'new_channel'], 'ruleEditor');

    console.log('✅ 测试完成！当前渠道:', manager.getAllChannels());
}

runTest().catch(console.error);
