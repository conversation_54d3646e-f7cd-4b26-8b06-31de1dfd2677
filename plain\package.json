{"name": "channel-detection-editor-e2e", "version": "1.0.0", "description": "Channel Detection Editor with E2E Testing", "main": "index.js", "scripts": {"test": "playwright test", "install-playwright": "playwright install", "install-deps": "npm install", "start": "npm run install-deps && npm run install-playwright"}, "devDependencies": {"@playwright/test": "^1.46.0"}, "keywords": ["channel-detection", "editor", "e2e-testing", "playwright"], "author": "", "license": "ISC"}